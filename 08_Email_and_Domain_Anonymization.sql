-- =====================================================================================
-- COMPLETEVIEW DATABASE ANONYMIZATION - STANDALONE MODULE
-- File: 08_Email_and_Domain_Anonymization.sql

-- =====================================================================================
-- This is a standalone, pre-configured SQL module that can be executed directly
-- in SQL Server Management Studio without requiring PowerShell configuration.
-- 
-- IMPORTANT: 
-- 1. BACKUP YOUR DATABASE BEFORE RUNNING THIS SCRIPT
-- 2. Review the configuration values below before execution
-- 3. Execute modules in numerical order (01, 02, 03, etc.)
-- =====================================================================================

-- =====================================================================================
-- CENTRALIZED CONFIGURATION LOADER
-- =====================================================================================
-- Load configuration from centralized table (eliminates config drift risk)
-- =====================================================================================

-- Verify configuration prerequisites
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    RAISERROR('AnonymizationConfig table not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

IF OBJECT_ID('dbo.fn_GetConfigValue') IS NULL
BEGIN
    RAISERROR('Configuration helper function fn_GetConfigValue not found. Please run Module 01 (Setup and Validation) first.', 16, 1);
END

-- Load configuration values from centralized table
DECLARE @ExecutionID VARCHAR(50) = 'MODULE08_' + REPLACE(CAST(NEWID() AS VARCHAR(36)), '-', '');
DECLARE @ModuleName VARCHAR(100) = 'Email and Domain Anonymization';

PRINT 'Starting ' + @ModuleName + '...';
PRINT 'Execution ID: ' + @ExecutionID;

DECLARE @AnonymizationSeed INT = CAST(dbo.fn_GetConfigValue('AnonymizationSeed') AS INT);
DECLARE @AnonymizedDomain NVARCHAR(100) = dbo.fn_GetConfigValue('AnonymizedDomain');
DECLARE @EmailSuffix NVARCHAR(100) = dbo.fn_GetConfigValue('EmailSuffix');
DECLARE @DefaultPassword NVARCHAR(100) = dbo.fn_GetConfigValue('DefaultPassword');
DECLARE @BatchSize INT = CAST(dbo.fn_GetConfigValue('BatchSize') AS INT);
DECLARE @DryRun BIT = CAST(dbo.fn_GetConfigValue('DryRun') AS BIT);
DECLARE @CommitChanges BIT = CAST(dbo.fn_GetConfigValue('CommitChanges') AS BIT);
DECLARE @UsePerformanceOptimizations BIT = CAST(dbo.fn_GetConfigValue('UsePerformanceOptimizations') AS BIT);

-- Additional configuration for this module
DECLARE @UserEmailPrefix VARCHAR(20) = dbo.fn_GetConfigValue('UserEmailPrefix');
DECLARE @AlertEmailPrefix VARCHAR(20) = dbo.fn_GetConfigValue('AlertEmailPrefix');
DECLARE @DomainPrefix VARCHAR(20) = dbo.fn_GetConfigValue('DomainPrefix');
DECLARE @ServiceAccountPrefix VARCHAR(20) = dbo.fn_GetConfigValue('ServiceAccountPrefix');
DECLARE @TemplatePrefix VARCHAR(20) = dbo.fn_GetConfigValue('TemplatePrefix');
DECLARE @StartTime DATETIME = GETDATE();

SET NOCOUNT ON;
SET XACT_ABORT ON;

BEGIN TRY
    BEGIN
        BEGIN TRAN;

-- Create pre-anonymization snapshot
PRINT 'Creating pre-anonymization snapshot...';
EXEC dbo.sp_CreatePreAnonymizationSnapshot 
    @ExecutionID = @ExecutionID,
    @ModuleName = @ModuleName;

PRINT '=======================================================================';
PRINT 'MODULE 08: EMAIL AND DOMAIN ANONYMIZATION';
PRINT '=======================================================================';
PRINT 'Execution Mode: ' + CASE WHEN @DryRun = 1 THEN 'DRY RUN (Preview Only)' ELSE 'LIVE EXECUTION' END;
PRINT CONCAT('Configured Prefixes: User=', @UserEmailPrefix, ', Alert=', @AlertEmailPrefix, ', Domain=', @DomainPrefix);
PRINT '';

-- Initialize tracking variables
DECLARE @TotalRecordsUpdated INT = 0;
DECLARE @RecordsUpdated INT = 0;

-- =====================================================================================
-- NOTIFICATION EMAILS ANONYMIZATION (Fixed implementation)
-- =====================================================================================
PRINT 'Processing notification email addresses...';

IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'NotificationEmails')
BEGIN
    DECLARE @NotifEmailRowCount INT = (SELECT COUNT(*) FROM dbo.NotificationEmails);
    
    IF @NotifEmailRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@NotifEmailRowCount AS VARCHAR) + ' notification email records to process.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: Notification email anonymization preview:';
            SELECT TOP 10
                EmailID,
                EmailAddress AS Current_EmailAddress,
                @UserEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailID, @AnonymizationSeed, 'EMAIL') AS VARCHAR), 6) + '@example.com' AS New_EmailAddress,
                DisplayName AS Current_DisplayName,
                'User ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailID, @AnonymizationSeed, 'EMAIL') AS VARCHAR), 6) AS New_DisplayName,
                GroupName AS Current_GroupName,
                'Group ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailID, @AnonymizationSeed, 'GROUP') AS VARCHAR), 6) AS New_GroupName
            FROM dbo.NotificationEmails;
        END
        ELSE
        BEGIN
            -- Anonymize notification emails using CTE
            WITH NotificationEmailCTE AS (
                SELECT EmailID,
                       @UserEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailID, @AnonymizationSeed, 'EMAIL') AS VARCHAR), 6) + '@example.com' AS NewEmailAddress,
                       'User ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailID, @AnonymizationSeed, 'EMAIL') AS VARCHAR), 6) AS NewDisplayName,
                       'Group ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailID, @AnonymizationSeed, 'GROUP') AS VARCHAR), 6) AS NewGroupName
                FROM dbo.NotificationEmails
            )
            UPDATE ne
            SET EmailAddress = cte.NewEmailAddress,
                DisplayName = cte.NewDisplayName,
                GroupName = cte.NewGroupName
            FROM dbo.NotificationEmails ne
            INNER JOIN NotificationEmailCTE cte ON ne.EmailID = cte.EmailID;
            
            DECLARE @NotifEmailUpdateCount INT = @@ROWCOUNT;
            SET @TotalRecordsUpdated = @TotalRecordsUpdated + @NotifEmailUpdateCount;
            
            -- Log changes
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
            BEGIN
                INSERT INTO dbo.AnonymizationLog (
                    Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                    ExecutionTime, Notes
                )
                VALUES ('Email Data', 'NotificationEmails', 'EmailData', @NotifEmailUpdateCount, 'Email Anonymization', 
                        GETDATE(), 'Email addresses, display names, and group names anonymized with ' + @UserEmailPrefix + ' pattern');
            END
            
            PRINT 'Updated ' + CAST(@NotifEmailUpdateCount AS VARCHAR) + ' notification email records';
        END
    END
    ELSE
    BEGIN
        PRINT 'No notification email records found to anonymize.';
    END
END
ELSE
BEGIN
    PRINT 'NotificationEmails table not found in database.';
END

-- =====================================================================================
-- SMTP CONFIGURATION ANONYMIZATION
-- =====================================================================================
PRINT '';
PRINT 'Processing SMTP configuration...';

-- Anonymize SMTP server settings
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'EmailServer')
BEGIN
    DECLARE @EmailServerRowCount INT = (SELECT COUNT(*) FROM dbo.EmailServer);
    
    IF @EmailServerRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@EmailServerRowCount AS VARCHAR) + ' EmailServer configuration records to process.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: EmailServer configuration anonymization preview:';
            SELECT TOP 10
                EmailServerId,
                Smtp AS Current_Smtp,
                'smtp.example.com' AS New_Smtp,
                UserName AS Current_Username,
                '[REDACTED-USERNAME]' AS New_Username,
                Password AS Current_Password,
                '[REDACTED-PASSWORD]' AS New_Password
            FROM dbo.EmailServer;
        END
        ELSE
        BEGIN
            -- Anonymize EmailServer configuration
            UPDATE dbo.EmailServer 
            SET Smtp = 'smtp.example.com',
                UserName = '[REDACTED-USERNAME]',
                Password = '[REDACTED-PASSWORD]';
            
            DECLARE @EmailServerUpdateCount INT;
            SET @EmailServerUpdateCount = @@ROWCOUNT;
            
            -- Log changes
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
            BEGIN
                INSERT INTO dbo.AnonymizationLog (
                    Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                    ExecutionTime, Notes
                )
                VALUES ('Email Data', 'EmailServer', 'Smtp,UserName,Password', @EmailServerUpdateCount, 'Email Server Configuration', 
                        GETDATE(), 'Email server settings and credentials anonymized with standard values');
            END
            
            PRINT 'Updated ' + CAST(@EmailServerUpdateCount AS VARCHAR) + ' EmailServer configuration records';
        END
    END
ELSE
    BEGIN
        PRINT 'EmailServer table not found - skipping email server configuration anonymization';
    END

-- EMAILMESSAGE ANONYMIZATION (SENDER DATA)
-- =====================================================================================
PRINT 'Processing EmailMessage sender data...';

-- Anonymize email message sender information
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'EmailMessage')
BEGIN
    DECLARE @EmailMessageRowCount INT = (SELECT COUNT(*) FROM dbo.EmailMessage);
    
    IF @EmailMessageRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@EmailMessageRowCount AS VARCHAR) + ' EmailMessage records to process.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: EmailMessage anonymization preview:';
            SELECT TOP 10
                EmailMessageId,
                SenderName AS Current_SenderName,
                'System Notifications' AS New_SenderName,
                SenderEmail AS Current_SenderEmail,
                '<EMAIL>' AS New_SenderEmail
            FROM dbo.EmailMessage;
        END
        ELSE
        BEGIN
            -- Anonymize EmailMessage sender data
            UPDATE dbo.EmailMessage 
            SET SenderName = 'System Notifications',
                SenderEmail = '<EMAIL>';
            
            DECLARE @EmailMessageUpdateCount INT;
            SET @EmailMessageUpdateCount = @@ROWCOUNT;
            
            -- Log changes
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
            BEGIN
                INSERT INTO dbo.AnonymizationLog (
                    Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                    ExecutionTime, Notes
                )
                VALUES ('Email Data', 'EmailMessage', 'SenderName,SenderEmail', @EmailMessageUpdateCount, 'Email Message Sender', 
                        GETDATE(), 'Email message sender information anonymized');
            END
            
            PRINT 'Updated ' + CAST(@EmailMessageUpdateCount AS VARCHAR) + ' EmailMessage records';
        END
    END
    ELSE
    BEGIN
        PRINT 'No SMTP configuration records found to anonymize.';
    END
END

-- =====================================================================================
-- DOMAIN REFERENCES ANONYMIZATION
-- =====================================================================================
PRINT '';
PRINT 'Processing domain references...';

-- Anonymize domain names in various configuration tables
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'DomainConfiguration')
BEGIN
    DECLARE @DomainRowCount INT = (SELECT COUNT(*) FROM dbo.DomainConfiguration);
    
    IF @DomainRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@DomainRowCount AS VARCHAR) + ' domain configuration records to process.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: Domain configuration anonymization preview:';
            SELECT TOP 10
                DomainID,
                DomainName AS Current_DomainName,
                @DomainPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(DomainID, @AnonymizationSeed, 'DOMAIN') AS VARCHAR), 6) + '.example.com' AS New_DomainName,
                DomainController AS Current_DomainController,
                'dc' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(DomainID, @AnonymizationSeed, 'DC') AS VARCHAR), 6) + '.example.com' AS New_DomainController,
                ServiceAccount AS Current_ServiceAccount,
                @ServiceAccountPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(DomainID, @AnonymizationSeed, 'SVC') AS VARCHAR), 6) AS New_ServiceAccount
            FROM dbo.DomainConfiguration;
        END
        ELSE
        BEGIN
            -- Anonymize domain configuration using CTE
            WITH DomainCTE AS (
                SELECT DomainID,
                       @DomainPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(DomainID, @AnonymizationSeed, 'DOMAIN') AS VARCHAR), 6) + '.example.com' AS NewDomainName,
                       'dc' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(DomainID, @AnonymizationSeed, 'DC') AS VARCHAR), 6) + '.example.com' AS NewDomainController,
                       @ServiceAccountPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(DomainID, @AnonymizationSeed, 'SVC') AS VARCHAR), 6) AS NewServiceAccount
                FROM dbo.DomainConfiguration
            )
            UPDATE dc
            SET DomainName = cte.NewDomainName,
                DomainController = cte.NewDomainController,
                ServiceAccount = cte.NewServiceAccount,
                ServicePassword = '[REDACTED-PASSWORD]'
            FROM dbo.DomainConfiguration dc
            INNER JOIN DomainCTE cte ON dc.DomainID = cte.DomainID;
            
            DECLARE @DomainUpdateCount INT;
            SET @DomainUpdateCount = @@ROWCOUNT;
            
            -- Log changes
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
            BEGIN
                INSERT INTO dbo.AnonymizationLog (
                    Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                    ExecutionTime, Notes
                )
                VALUES ('Email Data', 'DomainConfiguration', 'DomainInfo', @DomainUpdateCount, 'Domain Configuration', 
                        GETDATE(), 'Domain names, controllers, and service accounts anonymized with ' + @DomainPrefix + ' and ' + @ServiceAccountPrefix + ' patterns');
            END
            
            PRINT 'Updated ' + CAST(@DomainUpdateCount AS VARCHAR) + ' domain configuration records';
        END
    END
    ELSE
    BEGIN
        PRINT 'No domain configuration records found to anonymize.';
    END
END

-- =====================================================================================
-- ALERT EMAIL CONFIGURATIONS (Added missing section)
-- =====================================================================================
PRINT '';
PRINT 'Processing alert email configurations...';

-- Anonymize alert email settings
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AlertEmails')
BEGIN
    DECLARE @AlertEmailRowCount INT = (SELECT COUNT(*) FROM dbo.AlertEmails);
    
    IF @AlertEmailRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@AlertEmailRowCount AS VARCHAR) + ' alert email records to process.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: Alert email anonymization preview:';
            SELECT TOP 10
                AlertID,
                EmailAddress AS Current_EmailAddress,
                @AlertEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(AlertID, @AnonymizationSeed, 'ALERT') AS VARCHAR), 6) + '@example.com' AS New_EmailAddress,
                ContactName AS Current_ContactName,
                'Alert Contact ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(AlertID, @AnonymizationSeed, 'CONTACT') AS VARCHAR), 6) AS New_ContactName
            FROM dbo.AlertEmails;
        END
        ELSE
        BEGIN
            -- Anonymize alert emails using CTE
            WITH AlertEmailCTE AS (
                SELECT AlertID,
                       @AlertEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(AlertID, @AnonymizationSeed, 'ALERT') AS VARCHAR), 6) + '@example.com' AS NewEmailAddress,
                       'Alert Contact ' + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(AlertID, @AnonymizationSeed, 'CONTACT') AS VARCHAR), 6) AS NewContactName
                FROM dbo.AlertEmails
            )
            UPDATE ae
            SET EmailAddress = cte.NewEmailAddress,
                ContactName = cte.NewContactName
            FROM dbo.AlertEmails ae
            INNER JOIN AlertEmailCTE cte ON ae.AlertID = cte.AlertID;
            
            DECLARE @AlertEmailUpdateCount INT = @@ROWCOUNT;
            SET @TotalRecordsUpdated = @TotalRecordsUpdated + @AlertEmailUpdateCount;
            
            -- Log changes
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
            BEGIN
                INSERT INTO dbo.AnonymizationLog (
                    Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                    ExecutionTime, Notes
                )
                VALUES ('Email Data', 'AlertEmails', 'EmailAddress,ContactName', @AlertEmailUpdateCount, 'Email Anonymization', 
                        GETDATE(), 'Alert email addresses and contact names anonymized with ' + @AlertEmailPrefix + ' pattern');
            END
            
            PRINT 'Updated ' + CAST(@AlertEmailUpdateCount AS VARCHAR) + ' alert email records';
        END
    END
    ELSE
    BEGIN
        PRINT 'No alert email records found to anonymize.';
    END
END
ELSE
BEGIN
    PRINT 'AlertEmails table not found in database.';
END

-- =====================================================================================
-- EMAIL TEMPLATES ANONYMIZATION (Fixed incomplete implementation)
-- =====================================================================================
PRINT '';
PRINT 'Processing email templates...';

-- Anonymize email template content
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'EmailTemplates')
BEGIN
    DECLARE @EmailTemplateRowCount INT = (SELECT COUNT(*) FROM dbo.EmailTemplates);
    
    IF @EmailTemplateRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@EmailTemplateRowCount AS VARCHAR) + ' email template records to process.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: Email templates anonymization preview:';
            SELECT TOP 10
                TemplateID,
                TemplateName AS Current_TemplateName,
                @TemplatePrefix + ' ' + RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY TemplateID) AS VARCHAR), 3) AS New_TemplateName,
                Subject AS Current_Subject,
                'Anonymized Email Subject' AS New_Subject,
                FromEmail AS Current_FromEmail,
                '<EMAIL>' AS New_FromEmail,
                ToEmail AS Current_ToEmail,
                '<EMAIL>' AS New_ToEmail
            FROM dbo.EmailTemplates;
        END
        ELSE
        BEGIN
            -- Anonymize email templates using CTE
            WITH EmailTemplateCTE AS (
                SELECT TemplateID,
                       @TemplatePrefix + ' ' + RIGHT('000' + CAST(ROW_NUMBER() OVER (ORDER BY TemplateID) AS VARCHAR), 3) AS NewTemplateName
                FROM dbo.EmailTemplates
            )
            UPDATE et
            SET TemplateName = cte.NewTemplateName,
                Subject = 'Anonymized Email Subject',
                FromEmail = '<EMAIL>',
                ToEmail = '<EMAIL>',
                Body = 'This is an anonymized email template body. Original content has been replaced for security purposes.',
                HtmlBody = '<html><body><p>This is an anonymized email template body. Original content has been replaced for security purposes.</p></body></html>'
            FROM dbo.EmailTemplates et
            INNER JOIN EmailTemplateCTE cte ON et.TemplateID = cte.TemplateID;
            
            DECLARE @EmailTemplateUpdateCount INT = @@ROWCOUNT;
            SET @TotalRecordsUpdated = @TotalRecordsUpdated + @EmailTemplateUpdateCount;
            
            -- Log changes
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
            BEGIN
                INSERT INTO dbo.AnonymizationLog (
                    Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                    ExecutionTime, Notes
                )
                VALUES ('Email Data', 'EmailTemplates', 'TemplateContent', @EmailTemplateUpdateCount, 'Email Template', 
                        GETDATE(), 'Email template names, subjects, and content anonymized with ' + @TemplatePrefix + ' pattern');
            END
            
            PRINT 'Updated ' + CAST(@EmailTemplateUpdateCount AS VARCHAR) + ' email template records';
        END
    END
    ELSE
    BEGIN
        PRINT 'No email template records found to anonymize.';
    END
END
ELSE
BEGIN
    PRINT 'EmailTemplates table not found in database.';
END

-- =====================================================================================
-- USER EMAIL ADDRESSES ANONYMIZATION (Fixed incomplete implementation)
-- =====================================================================================
PRINT '';
PRINT 'Processing additional user email addresses...';

-- Anonymize any remaining email addresses in User table
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'User') 
   AND EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID('dbo.[User]') AND name = 'Email')
BEGIN
    DECLARE @UserEmailRowCount INT = (SELECT COUNT(*) FROM dbo.[User] WHERE Email IS NOT NULL);
    
    IF @UserEmailRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@UserEmailRowCount AS VARCHAR) + ' user email addresses to verify/update.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: User email addresses verification:';
            SELECT TOP 5
                UserID,
                Username,
                Email AS Current_Email,
                CASE 
                    WHEN Email NOT LIKE '%@example.com' THEN @UserEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USEREMAIL') AS VARCHAR), 6) + '@example.com'
                    ELSE Email
                END AS New_Email
            FROM dbo.[User] 
            WHERE Email IS NOT NULL 
            AND Email NOT LIKE '%@example.com';
        END
        ELSE
        BEGIN
            -- Check if emails are already anonymized
            DECLARE @UnanonymizedEmails INT = (SELECT COUNT(*) FROM dbo.[User] 
                                             WHERE Email IS NOT NULL 
                                             AND Email NOT LIKE '%@example.com');
            
            IF @UnanonymizedEmails > 0
            BEGIN
                -- Anonymize user emails using CTE
                WITH UserEmailCTE AS (
                    SELECT UserID,
                           @UserEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(UserID, @AnonymizationSeed, 'USEREMAIL') AS VARCHAR), 6) + '@example.com' AS NewEmail
                    FROM dbo.[User]
                    WHERE Email IS NOT NULL 
                    AND Email NOT LIKE '%@example.com'
                )
                UPDATE u
                SET Email = cte.NewEmail
                FROM dbo.[User] u
                INNER JOIN UserEmailCTE cte ON u.UserID = cte.UserID;
                
                DECLARE @UserEmailUpdateCount INT = @@ROWCOUNT;
                SET @TotalRecordsUpdated = @TotalRecordsUpdated + @UserEmailUpdateCount;
                
                -- Log changes
                IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
                BEGIN
                    INSERT INTO dbo.AnonymizationLog (
                        Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                        ExecutionTime, Notes
                    )
                    VALUES ('Email Data', 'User', 'Email', @UserEmailUpdateCount, 'Email Anonymization', 
                            GETDATE(), 'Additional user emails anonymized with ' + @UserEmailPrefix + ' pattern');
                END
                
                PRINT 'Updated ' + CAST(@UserEmailUpdateCount AS VARCHAR) + ' additional user email addresses';
            END
            ELSE
            BEGIN
                PRINT 'All user email addresses are already anonymized.';
            END
        END
    END
    ELSE
    BEGIN
        PRINT 'No user email addresses found to anonymize.';
    END
END
ELSE
BEGIN
    PRINT 'User table or Email column not found in database.';
END

-- =====================================================================================
-- EMAIL MESSAGE SENDER ANONYMIZATION (Fixed missing implementation)
-- =====================================================================================
PRINT '';
PRINT 'Processing email message sender information...';

IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'EmailMessage')
BEGIN
    DECLARE @EmailMessageRowCount INT = (SELECT COUNT(*) FROM dbo.EmailMessage WHERE SenderName IS NOT NULL OR SenderEmail IS NOT NULL);
    
    IF @EmailMessageRowCount > 0
    BEGIN
        PRINT 'Found ' + CAST(@EmailMessageRowCount AS VARCHAR) + ' email message records to process.';
        
        IF @DryRun = 1
        BEGIN
            PRINT 'DRY RUN: EmailMessage anonymization preview:';
            SELECT TOP 10
                EmailMessageId,
                SenderName AS Current_SenderName,
                'System Notifications' AS New_SenderName,
                SenderEmail AS Current_SenderEmail,
                '<EMAIL>' AS New_SenderEmail,
                RecipientEmail AS Current_RecipientEmail,
                CASE 
                    WHEN RecipientEmail IS NOT NULL 
                    THEN @UserEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailMessageId, @AnonymizationSeed, 'RECIPIENT') AS VARCHAR), 6) + '@example.com'
                    ELSE NULL 
                END AS New_RecipientEmail
            FROM dbo.EmailMessage
            WHERE SenderName IS NOT NULL OR SenderEmail IS NOT NULL OR RecipientEmail IS NOT NULL;
        END
        ELSE
        BEGIN
            -- Anonymize EmailMessage sender and recipient data using CTE
            WITH EmailMessageCTE AS (
                SELECT EmailMessageId,
                       CASE 
                           WHEN RecipientEmail IS NOT NULL 
                           THEN @UserEmailPrefix + RIGHT('000000' + CAST(dbo.fn_GetAnonymizedSequence(EmailMessageId, @AnonymizationSeed, 'RECIPIENT') AS VARCHAR), 6) + '@example.com'
                           ELSE NULL 
                       END AS NewRecipientEmail
                FROM dbo.EmailMessage
                WHERE RecipientEmail IS NOT NULL
            )
            UPDATE em
            SET SenderName = 'System Notifications',
                SenderEmail = '<EMAIL>',
                RecipientEmail = ISNULL(cte.NewRecipientEmail, em.RecipientEmail)
            FROM dbo.EmailMessage em
            LEFT JOIN EmailMessageCTE cte ON em.EmailMessageId = cte.EmailMessageId
            WHERE em.SenderName IS NOT NULL OR em.SenderEmail IS NOT NULL OR em.RecipientEmail IS NOT NULL;
            
            DECLARE @EmailMessageUpdateCount INT = @@ROWCOUNT;
            SET @TotalRecordsUpdated = @TotalRecordsUpdated + @EmailMessageUpdateCount;
            
            -- Log changes
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
            BEGIN
                INSERT INTO dbo.AnonymizationLog (
                    Category, TableName, ColumnName, RowsAffected, AnonymizationType, 
                    ExecutionTime, Notes
                )
                VALUES ('Email Data', 'EmailMessage', 'SenderName,SenderEmail,RecipientEmail', @EmailMessageUpdateCount, 'Email Message Sender', 
                        GETDATE(), 'Email message sender and recipient information anonymized');
            END
            
            PRINT 'Updated ' + CAST(@EmailMessageUpdateCount AS VARCHAR) + ' EmailMessage records';
        END
    END
    ELSE
    BEGIN
        PRINT 'No email message records found to anonymize.';
    END
END
ELSE
BEGIN
    PRINT 'EmailMessage table not found in database.';
END

-- =====================================================================================
-- MODULE COMPLETION
-- =====================================================================================
PRINT '';
PRINT 'Module 08: Email and Domain Anonymization completed';
PRINT 'Total records updated: ' + CAST(@TotalRecordsUpdated AS VARCHAR);
PRINT 'Total execution time: ' + CAST(DATEDIFF(SECOND, @StartTime, GETDATE()) AS VARCHAR) + ' seconds';
PRINT '=======================================================================';

    -- Create post-anonymization snapshot
    IF @DryRun = 0
    BEGIN
        PRINT 'Creating post-anonymization snapshot...';
        EXEC dbo.sp_CreatePostAnonymizationSnapshot
            @ExecutionID = @ExecutionID,
            @ModuleName = @ModuleName;
    END

    IF @DryRun = 1
        ROLLBACK TRAN
    ELSE
        COMMIT TRAN;

    END -- Close the BEGIN block from line 60

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRAN;
    
    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    
    PRINT 'ERROR in Email and Domain Anonymization: ' + @ErrorMessage;
    
    -- Log the error if possible
    IF OBJECT_ID('dbo.AnonymizationLog') IS NOT NULL
    BEGIN
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, AnonymizationType, Notes)
        VALUES ('Email and Domain', 'ERROR', 'MODULE_FAILURE', 0, 'Error', @ErrorMessage);
    END
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH

